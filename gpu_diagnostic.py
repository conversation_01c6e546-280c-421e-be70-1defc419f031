#!/usr/bin/env python3
"""
GPU Diagnostic Script
Check PyTorch CUDA installation and GPU availability
"""

import torch
import sys

def check_gpu_availability():
    """Check GPU and CUDA availability"""
    print("🔍 GPU Diagnostic Report")
    print("=" * 50)
    
    # Check PyTorch version
    print(f"PyTorch Version: {torch.__version__}")
    
    # Check CUDA availability
    cuda_available = torch.cuda.is_available()
    print(f"CUDA Available: {cuda_available}")
    
    if cuda_available:
        # CUDA details
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")
        print(f"Number of GPUs: {torch.cuda.device_count()}")
        
        # GPU details
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.2f} GB)")
        
        # Current device
        current_device = torch.cuda.current_device()
        print(f"Current Device: {current_device}")
        
        # Test GPU tensor creation
        try:
            test_tensor = torch.randn(10, 10).cuda()
            print("✅ GPU tensor creation: SUCCESS")
            
            # Memory info
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"GPU Memory - Allocated: {allocated:.2f} GB, Reserved: {reserved:.2f} GB")
            
        except Exception as e:
            print(f"❌ GPU tensor creation: FAILED - {e}")
    else:
        print("❌ CUDA not available")
        print("\nPossible reasons:")
        print("1. PyTorch was installed without CUDA support")
        print("2. CUDA drivers not installed")
        print("3. CUDA version mismatch")
        print("4. GPU not detected")
    
    # Check if transformers can use GPU
    try:
        from transformers import pipeline
        print(f"\n📦 Transformers version: {__import__('transformers').__version__}")
        
        if cuda_available:
            # Test with a small model
            print("Testing transformers GPU usage...")
            test_pipe = pipeline("text-generation", model="gpt2", device=0)
            print("✅ Transformers GPU usage: SUCCESS")
        else:
            print("⚠️  Transformers will use CPU only")
            
    except Exception as e:
        print(f"❌ Transformers test: FAILED - {e}")
    
    print("\n" + "=" * 50)
    return cuda_available

if __name__ == "__main__":
    check_gpu_availability()
