#!/usr/bin/env python3
"""
Test actual model loading to see where the CPU fallback happens
"""

import torch
import sys
import os
from chatbot import CL<PERSON><PERSON>bot

def test_actual_loading():
    """Test the actual model loading process"""
    print("🔍 Testing Actual Model Loading Process")
    print("=" * 50)
    
    # Show environment
    print(f"Python executable: {sys.executable}")
    print(f"Working directory: {os.getcwd()}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
    
    # Create chatbot exactly like the main function does
    print(f"\n📋 Creating chatbot...")
    chatbot = CLIChatbot("chatbot_config.json")
    
    # Show initial state
    print(f"Initial model manager state:")
    if chatbot.model_manager:
        print(f"  use_gpu: {chatbot.model_manager.use_gpu}")
        print(f"  device: {chatbot.model_manager.device}")
    
    # Try to load the model
    print(f"\n🤖 Loading model...")
    try:
        success = chatbot.load_model()
        print(f"Model loading success: {success}")
        
        # Show final state
        if chatbot.model_manager:
            print(f"\nFinal model manager state:")
            print(f"  use_gpu: {chatbot.model_manager.use_gpu}")
            print(f"  device: {chatbot.model_manager.device}")
            print(f"  model loaded: {chatbot.model_manager.model is not None}")
            
            # Show model info like the chatbot does
            print(f"\n🔧 Model Info (like chatbot shows):")
            chatbot._show_model_info()
            
            # Check where the model actually is
            if chatbot.model_manager.model is not None:
                print(f"\nActual model device: {next(chatbot.model_manager.model.parameters()).device}")
            
    except Exception as e:
        print(f"❌ Error during model loading: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_actual_loading()
