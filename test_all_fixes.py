#!/usr/bin/env python3
"""
Comprehensive test to verify all fixes are working correctly
"""

from config import ChatbotConfig
from model_manager import LLMModelManager
import logging

# Set up logging to see all messages
logging.basicConfig(level=logging.INFO)

def test_all_fixes():
    """Test all the fixes we implemented"""
    print("🔍 Testing All Fixes")
    print("=" * 60)
    
    # Test 1: Dynamic Parameters
    print("\n1️⃣ Testing Dynamic Parameters")
    print("-" * 40)
    
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    print(f"Current model: {current_model}")
    
    # Test generation parameters
    gen_params = config.get_generation_params(current_model)
    print(f"Generation parameters for {current_model}:")
    for key, value in gen_params.items():
        print(f"  {key}: {value}")
    
    # Test prompt format
    prompt_format = config.get_prompt_format(current_model)
    print(f"Prompt format: {prompt_format}")
    
    # Test 2: Model Manager Parameter Updates
    print("\n2️⃣ Testing Model Manager Parameter Updates")
    print("-" * 40)
    
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=config.use_gpu()
    )
    
    print("Updating generation parameters...")
    model_manager.update_generation_params(**gen_params)
    
    print("Setting prompt format...")
    model_manager.prompt_format = prompt_format
    
    # Test 3: Auto-detection for different models
    print("\n3️⃣ Testing Auto-detection for Different Models")
    print("-" * 40)
    
    test_models = [
        "meta-llama/Llama-3.2-3B",  # Should auto-detect Llama-3.2 params
        "microsoft/phi-3-mini",     # Should auto-detect Phi params
        "Salesforce/codegen-2B-mono", # Should auto-detect code params
        "unknown/model"             # Should use defaults
    ]
    
    for model in test_models:
        print(f"\nModel: {model}")
        params = config.get_generation_params(model)
        format_type = config.get_prompt_format(model)
        print(f"  Max tokens: {params.get('max_new_tokens', 'default')}")
        print(f"  Temperature: {params.get('temperature', 'default')}")
        print(f"  Format: {format_type}")
    
    # Test 4: Configuration saving and loading
    print("\n4️⃣ Testing Configuration Persistence")
    print("-" * 40)
    
    # Add a new model with specific parameters
    test_model = "test/model-1b"
    test_params = {
        "max_new_tokens": 200,
        "temperature": 0.5,
        "top_p": 0.8,
        "do_sample": True,
        "repetition_penalty": 1.2
    }
    
    print(f"Adding model-specific parameters for {test_model}...")
    success = config.set_model_specific_params(
        test_model, 
        generation_params=test_params,
        prompt_format="instruct"
    )
    
    if success:
        print("✅ Successfully saved model-specific parameters")
        
        # Verify they were saved
        retrieved_params = config.get_generation_params(test_model)
        retrieved_format = config.get_prompt_format(test_model)
        
        print(f"Retrieved parameters: {retrieved_params}")
        print(f"Retrieved format: {retrieved_format}")
        
        # Check if they match
        params_match = all(retrieved_params.get(k) == v for k, v in test_params.items())
        format_match = retrieved_format == "instruct"
        
        if params_match and format_match:
            print("✅ Parameters correctly saved and retrieved")
        else:
            print("❌ Parameter mismatch detected")
    else:
        print("❌ Failed to save model-specific parameters")
    
    print("\n🎉 All tests completed!")
    print("=" * 60)

if __name__ == "__main__":
    test_all_fixes()
