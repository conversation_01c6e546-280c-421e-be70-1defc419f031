#!/usr/bin/env python3
"""
Check GPU memory usage and availability
"""

import torch
import gc

def check_gpu_memory():
    """Check GPU memory status"""
    print("🔍 GPU Memory Diagnostic")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return
    
    # Get GPU info
    device = torch.cuda.current_device()
    gpu_name = torch.cuda.get_device_name(device)
    gpu_props = torch.cuda.get_device_properties(device)
    total_memory = gpu_props.total_memory / 1024**3
    
    print(f"GPU: {gpu_name}")
    print(f"Total Memory: {total_memory:.2f} GB")
    
    # Check current memory usage
    allocated = torch.cuda.memory_allocated(device) / 1024**3
    reserved = torch.cuda.memory_reserved(device) / 1024**3
    free = total_memory - reserved
    
    print(f"Currently Allocated: {allocated:.2f} GB")
    print(f"Currently Reserved: {reserved:.2f} GB")
    print(f"Available: {free:.2f} GB")
    
    # Clear any existing GPU memory
    print(f"\nClearing GPU cache...")
    torch.cuda.empty_cache()
    gc.collect()
    
    allocated_after = torch.cuda.memory_allocated(device) / 1024**3
    reserved_after = torch.cuda.memory_reserved(device) / 1024**3
    free_after = total_memory - reserved_after
    
    print(f"After clearing:")
    print(f"  Allocated: {allocated_after:.2f} GB")
    print(f"  Reserved: {reserved_after:.2f} GB")
    print(f"  Available: {free_after:.2f} GB")
    
    # Estimate model memory requirements
    print(f"\n📊 Model Memory Estimates:")
    print(f"Llama-3.2-1B (float16): ~2.0 GB")
    print(f"Llama-3.2-1B (float32): ~4.0 GB")
    
    if free_after < 2.5:
        print(f"⚠️  WARNING: Available memory ({free_after:.2f} GB) might be insufficient")
        print(f"   This could cause fallback to CPU")
    else:
        print(f"✅ Sufficient memory available for GPU loading")
    
    # Test tensor allocation
    print(f"\n🧪 Testing GPU tensor allocation...")
    try:
        # Try to allocate a 1GB tensor
        test_size = 1024 * 1024 * 256  # 1GB in float32
        test_tensor = torch.randn(test_size, device='cuda')
        print(f"✅ Successfully allocated 1GB test tensor")
        
        # Check memory after allocation
        allocated_test = torch.cuda.memory_allocated(device) / 1024**3
        print(f"Memory after test allocation: {allocated_test:.2f} GB")
        
        # Clean up
        del test_tensor
        torch.cuda.empty_cache()
        print(f"✅ Test tensor cleaned up")
        
    except Exception as e:
        print(f"❌ Failed to allocate test tensor: {e}")
        print(f"   This indicates GPU memory issues")

if __name__ == "__main__":
    check_gpu_memory()
