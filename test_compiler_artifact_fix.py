#!/usr/bin/env python3
"""
Test the fix for compiler/registrar artifact responses
"""

from config import Cha<PERSON><PERSON><PERSON>onfig
from model_manager import LL<PERSON>ode<PERSON>Mana<PERSON>

def test_compiler_artifact_fix():
    """Test the fix for compiler artifact responses"""
    print("🔍 Testing Compiler Artifact Fix")
    print("=" * 60)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    # Create model manager (without loading the actual model)
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    gen_params = config.get_generation_params(current_model)
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = config.get_prompt_format(current_model)
    
    # Test the specific problematic response
    test_cases = [
        {
            "input": "do you know Python programming language?",
            "raw_response": "#docomputeregistrar#adocompiler#acompileradocompiler@authorcompilera.",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        },
        {
            "input": "Python?",
            "raw_response": "#compiler#registrar#author@compiler",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        },
        {
            "input": "programming",
            "raw_response": "@authorcompiler",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        },
        {
            "input": "code",
            "raw_response": "#registrar#compiler@author",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        }
    ]
    
    print(f"Testing {len(test_cases)} compiler/registrar artifact responses:")
    print("-" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        raw_response = test_case["raw_response"]
        expected = test_case["expected"]
        
        print(f"\n{i}. Input: '{user_input}'")
        print(f"   Raw response: '{raw_response}'")
        
        # Test the cleaning
        cleaned_response = model_manager._clean_response(raw_response, user_input)
        print(f"   Cleaned response: '{cleaned_response}'")
        print(f"   Expected: '{expected}'")
        
        if cleaned_response == expected:
            print("   ✅ PASSED")
        else:
            print("   ❌ FAILED")
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL COMPILER ARTIFACT TESTS PASSED!")
        print("The chatbot will now respond appropriately to programming questions")
        print("instead of giving compiler/registrar artifacts.")
    else:
        print("❌ Some tests failed. Need more work on compiler artifact filtering.")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    test_compiler_artifact_fix()
