#!/usr/bin/env python3
"""
Test GPU Configuration
Debug why the chatbot is using CPU instead of GPU
"""

import torch
from config import ChatbotConfig
from model_manager import LLMModelManager

def test_gpu_config():
    """Test the GPU configuration in the chatbot"""
    print("🔍 Testing Chatbot GPU Configuration")
    print("=" * 50)
    
    # Check PyTorch CUDA
    print(f"PyTorch CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
    
    # Load config
    print("\n📋 Loading Configuration...")
    config = ChatbotConfig("chatbot_config.json")
    
    # Check config settings
    use_gpu_config = config.use_gpu()
    current_model = config.get_current_model()
    print(f"Config use_gpu: {use_gpu_config}")
    print(f"Current model: {current_model}")
    
    # Test model manager initialization
    print("\n🤖 Testing Model Manager...")
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=use_gpu_config
    )
    
    print(f"Model Manager use_gpu: {model_manager.use_gpu}")
    print(f"Model Manager device: {model_manager.device}")
    
    # Check the logic
    print(f"\nDebugging GPU detection:")
    print(f"  use_gpu parameter: {use_gpu_config}")
    print(f"  torch.cuda.is_available(): {torch.cuda.is_available()}")
    print(f"  use_gpu AND torch.cuda.is_available(): {use_gpu_config and torch.cuda.is_available()}")
    
    # Test model loading (without actually loading the full model)
    print(f"\n🔧 Model Manager Info:")
    info = model_manager.get_model_info()
    for key, value in info.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_gpu_config()
