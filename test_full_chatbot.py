#!/usr/bin/env python3
"""
Test Full Chatbot Initialization
Replicate exactly what the chatbot does to find the GPU issue
"""

import sys
from chatbot import C<PERSON><PERSON><PERSON><PERSON>

def test_full_chatbot():
    """Test the full chatbot initialization process"""
    print("🔍 Testing Full Chatbot Initialization")
    print("=" * 50)
    
    # Replicate exactly what main() does
    print("Creating chatbot...")
    chatbot = CLIChatbot("chatbot_config.json")
    
    print("Initializing model...")
    chatbot._initialize_model()
    
    print("Getting model info...")
    if chatbot.model_manager:
        info = chatbot.model_manager.get_model_info()
        print(f"\n🔧 Model Manager Info:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        print(f"\n🔧 Direct Properties:")
        print(f"  use_gpu: {chatbot.model_manager.use_gpu}")
        print(f"  device: {chatbot.model_manager.device}")
        
        # Test the _show_model_info method
        print(f"\n🔧 _show_model_info output:")
        chatbot._show_model_info()
    else:
        print("❌ Model manager is None")

if __name__ == "__main__":
    test_full_chatbot()
