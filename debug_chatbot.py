#!/usr/bin/env python3
"""
Debug version of chatbot that shows all arguments and configuration
"""

import argparse
import sys
import torch
from chatbot import CLIChatbot

def debug_main():
    """Debug version of main function"""
    print("🔍 Debug Chatbot Startup")
    print("=" * 50)
    
    # Show command line arguments
    print(f"Command line arguments: {sys.argv}")
    
    # Parse arguments
    parser = argparse.ArgumentParser(description="LLM Chatbot with GPU Support")
    parser.add_argument("--config", default="chatbot_config.json", 
                       help="Configuration file path")
    parser.add_argument("--model", help="Model to use (overrides config)")
    parser.add_argument("--no-gpu", action="store_true", 
                       help="Disable GPU usage")
    
    args = parser.parse_args()
    
    print(f"Parsed arguments:")
    print(f"  config: {args.config}")
    print(f"  model: {args.model}")
    print(f"  no_gpu: {args.no_gpu}")
    
    # Check PyTorch CUDA
    print(f"\nPyTorch CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
    
    # Create chatbot
    print(f"\nCreating chatbot with config: {args.config}")
    chatbot = CLIChatbot(args.config)
    
    # Show initial config
    print(f"Initial use_gpu setting: {chatbot.config.use_gpu()}")
    
    # Override model if specified
    if args.model:
        print(f"Overriding model to: {args.model}")
        chatbot.config.set_current_model(args.model)
        chatbot._initialize_model()
    
    # Override GPU setting if specified
    if args.no_gpu:
        print("⚠️  GPU DISABLED by --no-gpu flag")
        chatbot.config.set_use_gpu(False)
        chatbot._initialize_model()
    
    # Show final model manager state
    if chatbot.model_manager:
        print(f"\nFinal model manager state:")
        print(f"  use_gpu: {chatbot.model_manager.use_gpu}")
        print(f"  device: {chatbot.model_manager.device}")
        
        # Show model info
        chatbot._show_model_info()
    
    print("\n" + "=" * 50)
    print("Debug complete. The chatbot would start here.")

if __name__ == "__main__":
    debug_main()
