#!/usr/bin/env python3
"""
Test script to verify the multi-persona response fix
This tests the specific issue reported by the user where the chatbot was giving
responses like: "Hi, I am the assistant. Student: I don't know what you are talking about... Professor: What do you mean? Assistants: The word assistant is a misnomer."
"""

from model_manager import LLMModelManager
from config import ChatbotConfig

def test_multi_persona_fix():
    """Test that multi-persona responses are properly cleaned"""
    
    # Initialize model manager
    config = ChatbotConfig()
    model_manager = LLMModelManager(
        model_name=config.get_current_model(),
        use_gpu=config.use_gpu()
    )
    
    # Test cases based on the reported issue
    test_cases = [
        {
            "name": "Original reported issue",
            "input": "hello",
            "raw_response": "Hi, I am the assistant. Student: I don't know what you are talking about... Professor: What do you mean? Assistants: The word assistant is a misnomer.",
            "should_not_contain": ["Student:", "Professor:", "Assistants:"]
        },
        {
            "name": "Multi-persona with names",
            "input": "hello", 
            "raw_response": "what is your name? Man: \"Granite\". A: Granite, yeah. It's my first game ever. You can't beat it! I've been playing since day one!",
            "should_not_contain": ["Man:", "A:"]
        },
        {
            "name": "Multi-persona with roles",
            "input": "hello",
            "raw_response": "hi\nAssistants: I have a question.\nManager: what do you want me to ask?\nA: Can you get rid of the old-school way of doing things, and make it more efficient?",
            "should_not_contain": ["Assistants:", "Manager:", "A:"]
        },
        {
            "name": "Complex multi-persona",
            "input": "hi",
            "raw_response": "hello Lucky: hey, what's up? Troublemaker: i'm looking for a friend. I was a little hesitant to tell him about the last time I had sex.",
            "should_not_contain": ["Lucky:", "Troublemaker:"]
        }
    ]
    
    print("Testing Multi-Persona Response Fix")
    print("=" * 50)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Input: '{test_case['input']}'")
        print(f"   Raw response: '{test_case['raw_response']}'")
        
        # Clean the response
        cleaned_response = model_manager._clean_response(test_case['raw_response'], test_case['input'])
        print(f"   Cleaned response: '{cleaned_response}'")
        
        # Check that problematic patterns are removed
        test_passed = True
        for pattern in test_case['should_not_contain']:
            if pattern in cleaned_response:
                print(f"   ❌ FAILED - Still contains: '{pattern}'")
                test_passed = False
                all_passed = False
        
        if test_passed:
            print("   ✅ PASSED - Multi-persona patterns removed")
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Multi-persona issue is FIXED!")
    else:
        print("❌ Some tests failed. Multi-persona issue still exists.")
    
    return all_passed

if __name__ == "__main__":
    test_multi_persona_fix()
