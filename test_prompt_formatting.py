#!/usr/bin/env python3
"""
Test prompt formatting to ensure proper Llama-3.2 instruction format
"""

from config import Chatbot<PERSON>onfig
from model_manager import LL<PERSON><PERSON><PERSON>Mana<PERSON>

def test_prompt_formatting():
    """Test the prompt formatting for Llama-3.2"""
    print("🔍 Testing Prompt Formatting for Llama-3.2")
    print("=" * 60)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    print(f"Current model: {current_model}")
    
    # Get prompt format
    prompt_format = config.get_prompt_format(current_model)
    print(f"Prompt format: {prompt_format}")
    
    # Create model manager
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False  # Don't load the actual model, just test formatting
    )
    
    # Set prompt format
    model_manager.prompt_format = prompt_format
    
    # Test different prompts
    test_prompts = [
        "hello",
        "how are you?",
        "do you know Python programming?",
        "what is the weather like today?"
    ]
    
    print(f"\n📝 Testing Prompt Formatting:")
    print("-" * 40)
    
    for i, user_input in enumerate(test_prompts, 1):
        print(f"\n{i}. User input: '{user_input}'")
        
        # Simulate chatbot's _prepare_prompt logic
        if prompt_format == "instruct":
            base_prompt = user_input  # Simple for instruction models
        else:
            base_prompt = f"Human: {user_input}\nAssistant:"
        
        print(f"   Base prompt: {repr(base_prompt)}")
        
        # Apply model manager formatting
        formatted_prompt = model_manager._format_prompt(base_prompt)
        print(f"   Formatted prompt: {repr(formatted_prompt)}")
        
        # Show what this looks like for Llama-3.2
        if prompt_format == "instruct":
            expected = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{user_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
            print(f"   Expected result: {repr(expected)}")
            
            if formatted_prompt == expected:
                print("   ✅ Formatting is correct!")
            else:
                print("   ❌ Formatting mismatch!")
        else:
            print("   ℹ️  Using default format")

if __name__ == "__main__":
    test_prompt_formatting()
