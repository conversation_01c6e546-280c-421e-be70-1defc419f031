#!/usr/bin/env python3
"""
Test the improved artifact cleaning with specific problematic responses
"""

from config import ChatbotConfig
from model_manager import LLMModelManager

def test_artifact_cleaning():
    """Test the artifact cleaning with various problematic responses"""
    print("🔍 Testing Improved Artifact Cleaning")
    print("=" * 60)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    # Create model manager (without loading the actual model)
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    gen_params = config.get_generation_params(current_model)
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = config.get_prompt_format(current_model)
    
    # Test cases with problematic responses
    test_cases = [
        {
            "input": "hello",
            "raw_response": "#1.",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "hello", 
            "raw_response": "#somedocument#adoc#documentation.",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "do you know Python?",
            "raw_response": "1.",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        },
        {
            "input": "how are you?",
            "raw_response": "###",
            "expected": "I'm doing well, thank you! How can I assist you?"
        },
        {
            "input": "hello",
            "raw_response": "   ",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "test",
            "raw_response": "#2.5",
            "expected": "I'm here to help! Could you please rephrase your question?"
        }
    ]
    
    print(f"Testing {len(test_cases)} problematic response cases:")
    print("-" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        raw_response = test_case["raw_response"]
        expected = test_case["expected"]
        
        print(f"\n{i}. Input: '{user_input}'")
        print(f"   Raw response: '{raw_response}'")
        
        # Test the cleaning
        cleaned_response = model_manager._clean_response(raw_response, user_input)
        print(f"   Cleaned response: '{cleaned_response}'")
        print(f"   Expected: '{expected}'")
        
        if cleaned_response == expected:
            print("   ✅ PASSED")
        else:
            print("   ❌ FAILED")
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! Artifact cleaning is working correctly.")
    else:
        print("❌ Some tests failed. Artifact cleaning needs more work.")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    test_artifact_cleaning()
