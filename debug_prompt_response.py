#!/usr/bin/env python3
"""
Debug prompt formatting and response generation
"""

from model_manager import LLMModelManager
from config import <PERSON><PERSON><PERSON><PERSON>onfig

def debug_prompt_response():
    """Debug the prompt formatting and response generation"""
    print("🔍 Debug Prompt and Response Generation")
    print("=" * 50)
    
    # Load config and create model manager
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=config.use_gpu()
    )
    
    # Update parameters
    gen_params = config.get_generation_params(current_model)
    model_manager.update_generation_params(**gen_params)
    
    # Set prompt format
    prompt_format = config.get_prompt_format(current_model)
    model_manager.prompt_format = prompt_format
    
    print(f"Model: {current_model}")
    print(f"Prompt format: {prompt_format}")
    
    # Test prompt formatting
    test_prompts = [
        "hello, how are you?",
        "can you program Python?", 
        "do you know Python?"
    ]
    
    for i, user_input in enumerate(test_prompts, 1):
        print(f"\n--- Test {i}: '{user_input}' ---")
        
        # Prepare prompt like chatbot does
        prompt = f"Human: {user_input}\nAssistant:"
        print(f"Basic prompt: {repr(prompt)}")
        
        # Format prompt like model manager does
        formatted_prompt = model_manager._format_prompt(prompt)
        print(f"Formatted prompt: {repr(formatted_prompt)}")
        
        # Test without loading the full model (just show what would happen)
        print(f"Expected format for Llama-3.2:")
        expected = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        print(f"  {repr(expected)}")

if __name__ == "__main__":
    debug_prompt_response()
