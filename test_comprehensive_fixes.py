#!/usr/bin/env python3
"""
Comprehensive test of all the improvements made to fix the chatbot response issues
"""

from config import Chat<PERSON><PERSON>onfig
from model_manager import LLMM<PERSON>lMana<PERSON>

def test_comprehensive_fixes():
    """Test all the improvements comprehensively"""
    print("🔍 Testing Comprehensive Chatbot Fixes")
    print("=" * 70)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    print(f"Current model: {current_model}")
    
    # Test 1: Model-specific parameter loading
    print(f"\n1️⃣ Testing Model-Specific Parameter Loading")
    print("-" * 50)
    
    gen_params = config.get_generation_params(current_model)
    prompt_format = config.get_prompt_format(current_model)
    
    print(f"Generation parameters for {current_model}:")
    for key, value in gen_params.items():
        print(f"  {key}: {value}")
    print(f"Prompt format: {prompt_format}")
    
    # Test 2: Model manager initialization with new parameters
    print(f"\n2️⃣ Testing Model Manager Initialization")
    print("-" * 50)
    
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = prompt_format
    
    print(f"Model manager configured:")
    print(f"  Temperature: {model_manager.temperature}")
    print(f"  Max new tokens: {model_manager.max_new_tokens}")
    print(f"  Top-p: {model_manager.top_p}")
    print(f"  Repetition penalty: {model_manager.repetition_penalty}")
    print(f"  Prompt format: {model_manager.prompt_format}")
    
    # Test 3: Prompt formatting
    print(f"\n3️⃣ Testing Prompt Formatting")
    print("-" * 50)
    
    test_prompts = ["hello", "how are you?", "can you help me with Python?"]
    
    for prompt in test_prompts:
        formatted = model_manager._format_prompt(prompt)
        print(f"Input: '{prompt}'")
        print(f"Formatted: '{formatted[:100]}{'...' if len(formatted) > 100 else ''}'")
        print()
    
    # Test 4: Response cleaning with problematic cases
    print(f"\n4️⃣ Testing Response Cleaning")
    print("-" * 50)
    
    test_cases = [
        {
            "input": "hello",
            "raw_response": "Hey, I'm here for the appointment with your doctor. Can you tell me what time it is? Patient: Hello! It's my mother-in-law!",
            "expected_contains": "Hello! How can I help you today?"
        },
        {
            "input": "hi",
            "raw_response": "I'm here for my appointment. Doctor: What seems to be the problem? Patient: I have a headache.",
            "expected_contains": "Hello! How can I help you today?"
        },
        {
            "input": "can you program Python?",
            "raw_response": "#docomputeregistrar#adocompiler#acompileradocompiler@authorcompilera.",
            "expected_contains": "I can help you with programming questions."
        },
        {
            "input": "hello",
            "raw_response": 'data-testid="read-dictionary"',
            "expected_contains": "Hello! How can I help you today?"
        },
        {
            "input": "what is Python?",
            "raw_response": "Python is a programming language. Student: I don't understand. Professor: Let me explain...",
            "expected_contains": "Python is a programming language."
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        raw_response = test_case["raw_response"]
        expected_contains = test_case["expected_contains"]
        
        print(f"\nTest {i}: Input: '{user_input}'")
        print(f"Raw response: '{raw_response}'")
        
        # Test the cleaning
        cleaned_response = model_manager._clean_response(raw_response, user_input)
        print(f"Cleaned response: '{cleaned_response}'")
        
        if expected_contains in cleaned_response:
            print("✅ PASSED")
        else:
            print("❌ FAILED")
            print(f"Expected to contain: '{expected_contains}'")
            all_passed = False
    
    # Test 5: Configuration persistence
    print(f"\n5️⃣ Testing Configuration Persistence")
    print("-" * 50)
    
    # Test saving model-specific parameters
    test_model = "test/example-model"
    test_params = {
        "max_new_tokens": 200,
        "temperature": 0.5,
        "repetition_penalty": 1.1
    }
    
    success = config.set_model_specific_params(
        test_model,
        generation_params=test_params,
        prompt_format="chat"
    )
    
    if success:
        print("✅ Successfully saved model-specific parameters")
        
        # Verify retrieval
        retrieved_params = config.get_generation_params(test_model)
        retrieved_format = config.get_prompt_format(test_model)
        
        params_match = all(retrieved_params.get(k) == v for k, v in test_params.items())
        format_match = retrieved_format == "chat"
        
        if params_match and format_match:
            print("✅ Parameters correctly saved and retrieved")
        else:
            print("❌ Parameter mismatch detected")
            all_passed = False
    else:
        print("❌ Failed to save model-specific parameters")
        all_passed = False
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 70)
    
    if all_passed:
        print("✅ ALL TESTS PASSED! The chatbot improvements are working correctly.")
        print("\n🚀 Key Improvements:")
        print("  • Enhanced response cleaning removes multi-persona artifacts")
        print("  • Model-specific parameter auto-detection for Llama-3.2")
        print("  • Improved prompt formatting for better responses")
        print("  • Contextual fallback responses for irrelevant outputs")
        print("  • Better repetition penalty and token limits")
    else:
        print("❌ Some tests failed. Please review the issues above.")

if __name__ == "__main__":
    test_comprehensive_fixes()
