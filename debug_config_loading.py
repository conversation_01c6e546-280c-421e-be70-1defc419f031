#!/usr/bin/env python3
"""
Debug configuration loading to find why GPU is not being used
"""

import torch
import json
from config import ChatbotConfig
from model_manager import LLMModelManager

def debug_config_loading():
    """Debug the configuration loading process step by step"""
    print("🔍 Debug Configuration Loading")
    print("=" * 50)
    
    # Check PyTorch CUDA first
    print(f"1. PyTorch CUDA Check:")
    print(f"   torch.cuda.is_available(): {torch.cuda.is_available()}")
    
    # Check the actual config file content
    print(f"\n2. Raw Config File Content:")
    try:
        with open("chatbot_config.json", 'r') as f:
            raw_config = json.load(f)
        print(f"   use_gpu in file: {raw_config.get('use_gpu', 'NOT FOUND')}")
        print(f"   current_model in file: {raw_config.get('current_model', 'NOT FOUND')}")
    except Exception as e:
        print(f"   Error reading config file: {e}")
    
    # Load config through ChatbotConfig class
    print(f"\n3. ChatbotConfig Class Loading:")
    config = ChatbotConfig("chatbot_config.json")
    use_gpu_from_config = config.use_gpu()
    current_model = config.get_current_model()
    print(f"   config.use_gpu(): {use_gpu_from_config}")
    print(f"   config.get_current_model(): {current_model}")
    
    # Test the logic in model manager initialization
    print(f"\n4. Model Manager Logic Test:")
    use_gpu_param = use_gpu_from_config
    cuda_available = torch.cuda.is_available()
    final_use_gpu = use_gpu_param and cuda_available
    
    print(f"   use_gpu parameter: {use_gpu_param}")
    print(f"   torch.cuda.is_available(): {cuda_available}")
    print(f"   use_gpu AND torch.cuda.is_available(): {final_use_gpu}")
    print(f"   Expected device: {'cuda' if final_use_gpu else 'cpu'}")
    
    # Create model manager with explicit parameters
    print(f"\n5. Model Manager Creation:")
    print(f"   Creating with use_gpu={use_gpu_from_config}")
    
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=use_gpu_from_config
    )
    
    print(f"   Actual model_manager.use_gpu: {model_manager.use_gpu}")
    print(f"   Actual model_manager.device: {model_manager.device}")
    
    # Check if there are any environment variables that might affect this
    import os
    print(f"\n6. Environment Variables:")
    cuda_vars = [var for var in os.environ.keys() if 'CUDA' in var.upper()]
    if cuda_vars:
        for var in cuda_vars:
            print(f"   {var}: {os.environ[var]}")
    else:
        print("   No CUDA-related environment variables found")
    
    # Check if there's a different config file being loaded
    print(f"\n7. Config File Path Check:")
    import os
    config_path = os.path.abspath("chatbot_config.json")
    print(f"   Absolute path: {config_path}")
    print(f"   File exists: {os.path.exists(config_path)}")
    print(f"   File size: {os.path.getsize(config_path) if os.path.exists(config_path) else 'N/A'} bytes")

if __name__ == "__main__":
    debug_config_loading()
