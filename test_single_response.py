#!/usr/bin/env python3
"""
Test a single response to debug the cleaning process
"""

from chatbot import CLIChatbot

def test_single_response():
    """Test a single response with debug output"""
    print("🔍 Testing Single Response with Debug")
    print("=" * 50)
    
    # Create chatbot
    chatbot = CLIChatbot("chatbot_config.json")
    
    # Load model
    print("Loading model...")
    if not chatbot.load_model():
        print("❌ Failed to load model")
        return
    
    print("✅ Model loaded successfully")
    
    # Test the failing case
    user_input = "hello, how are you?"
    print(f"\nTesting: '{user_input}'")
    
    # Generate response with debug output
    response = chatbot._generate_response(user_input)
    print(f"Final response: '{response}'")

if __name__ == "__main__":
    test_single_response()
