#!/usr/bin/env python3
"""
Debug the specific response issue with "#somedocument#adoc#documentation."
"""

from config import ChatbotConfig
from model_manager import <PERSON>MModelManager

def debug_response_issue():
    """Debug what's happening with the response generation"""
    print("🔍 Debugging Response Issue")
    print("=" * 60)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    print(f"Current model: {current_model}")
    
    # Get configuration
    gen_params = config.get_generation_params(current_model)
    prompt_format = config.get_prompt_format(current_model)
    
    print(f"Generation parameters: {gen_params}")
    print(f"Prompt format: {prompt_format}")
    
    # Create model manager (without loading the actual model)
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = prompt_format
    
    # Test the prompt formatting pipeline
    user_input = "hello"
    
    print(f"\n📝 Step-by-step debugging for input: '{user_input}'")
    print("-" * 50)
    
    # Step 1: <PERSON>t<PERSON>'s _prepare_prompt logic
    if prompt_format == "instruct":
        base_prompt = user_input  # Simple for instruction models
    else:
        base_prompt = f"Human: {user_input}\nAssistant:"
    
    print(f"1. Base prompt from chatbot: {repr(base_prompt)}")
    
    # Step 2: Model manager's _format_prompt
    formatted_prompt = model_manager._format_prompt(base_prompt)
    print(f"2. Formatted prompt: {repr(formatted_prompt)}")
    
    # Step 3: Check if this is the expected Llama-3.2 format
    if prompt_format == "instruct":
        expected = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{user_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        print(f"3. Expected format: {repr(expected)}")
        
        if formatted_prompt == expected:
            print("   ✅ Prompt formatting is correct!")
        else:
            print("   ❌ Prompt formatting is incorrect!")
            print(f"   Difference: {repr(formatted_prompt)} != {repr(expected)}")
    
    # Step 4: Test response cleaning with the problematic response
    problematic_response = "#somedocument#adoc#documentation."
    print(f"\n4. Testing response cleaning with: {repr(problematic_response)}")
    
    cleaned_response = model_manager._clean_response(problematic_response, base_prompt)
    print(f"   Cleaned response: {repr(cleaned_response)}")
    
    # Step 5: Check what parameters are actually being used
    print(f"\n5. Actual generation parameters being used:")
    print(f"   max_new_tokens: {getattr(model_manager, 'max_new_tokens', 'NOT SET')}")
    print(f"   temperature: {getattr(model_manager, 'temperature', 'NOT SET')}")
    print(f"   top_p: {getattr(model_manager, 'top_p', 'NOT SET')}")
    print(f"   repetition_penalty: {getattr(model_manager, 'repetition_penalty', 'NOT SET')}")
    print(f"   no_repeat_ngram_size: {getattr(model_manager, 'no_repeat_ngram_size', 'NOT SET')}")
    print(f"   prompt_format: {getattr(model_manager, 'prompt_format', 'NOT SET')}")
    
    # Step 6: Recommendations
    print(f"\n6. Recommendations:")
    if prompt_format != "instruct":
        print("   ❌ Prompt format should be 'instruct' for Llama-3.2-1B")
    else:
        print("   ✅ Prompt format is correct")
    
    if getattr(model_manager, 'max_new_tokens', 0) < 512:
        print("   ❌ max_new_tokens should be 512 for Llama-3.2-1B")
    else:
        print("   ✅ max_new_tokens is optimized")

if __name__ == "__main__":
    debug_response_issue()
