#!/usr/bin/env python3
"""
Test the fix for HTML testing artifact responses
"""

from config import ChatbotConfig
from model_manager import LLMModelManager

def test_html_testing_artifact_fix():
    """Test the fix for HTML testing artifact responses"""
    print("🔍 Testing HTML Testing Artifact Fix")
    print("=" * 60)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    # Create model manager (without loading the actual model)
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    gen_params = config.get_generation_params(current_model)
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = config.get_prompt_format(current_model)
    
    # Test the specific problematic response
    test_cases = [
        {
            "input": "hello",
            "raw_response": '## `data-testid="read-dictionary" />` (for "Read dictionary") should be the same as "test-reading".',
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "hi",
            "raw_response": '`data-testid="button" />` should be "test-button"',
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "greetings",
            "raw_response": "## `<div data-testid='component' />` for testing",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "hello",
            "raw_response": "test-reading should be the same as data-testid",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "hey",
            "raw_response": '`<input data-testid="field" />` (for input field)',
            "expected": "Hello! How can I help you today?"
        }
    ]
    
    print(f"Testing {len(test_cases)} HTML testing artifact responses:")
    print("-" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        raw_response = test_case["raw_response"]
        expected = test_case["expected"]
        
        print(f"\n{i}. Input: '{user_input}'")
        print(f"   Raw response: '{raw_response}'")
        
        # Test the cleaning
        cleaned_response = model_manager._clean_response(raw_response, user_input)
        print(f"   Cleaned response: '{cleaned_response}'")
        print(f"   Expected: '{expected}'")
        
        if cleaned_response == expected:
            print("   ✅ PASSED")
        else:
            print("   ❌ FAILED")
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL HTML TESTING ARTIFACT TESTS PASSED!")
        print("The chatbot will now respond appropriately to greetings")
        print("instead of giving HTML testing documentation.")
    else:
        print("❌ Some tests failed. Need more work on HTML testing artifact filtering.")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    test_html_testing_artifact_fix()
