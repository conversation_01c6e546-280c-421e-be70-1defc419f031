#!/usr/bin/env python3
"""
Test the fix for the specific career response issue
"""

from config import ChatbotConfig
from model_manager import LLMModelManager

def test_career_response_fix():
    """Test the fix for the career response issue"""
    print("🔍 Testing Career Response Fix")
    print("=" * 60)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    # Create model manager (without loading the actual model)
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    gen_params = config.get_generation_params(current_model)
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = config.get_prompt_format(current_model)
    
    # Test the specific problematic response
    test_cases = [
        {
            "input": "hello",
            "raw_response": "# I am a student and i want to become an engineer, what should be my career path?",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "hi",
            "raw_response": "I am a student and want to become an engineer",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "hello",
            "raw_response": "# career path for engineering student",
            "expected": "Hello! How can I help you today?"
        },
        {
            "input": "greetings",
            "raw_response": "become an engineer what should be my career path",
            "expected": "Hello! How can I help you today?"
        }
    ]
    
    print(f"Testing {len(test_cases)} career-related problematic responses:")
    print("-" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        raw_response = test_case["raw_response"]
        expected = test_case["expected"]
        
        print(f"\n{i}. Input: '{user_input}'")
        print(f"   Raw response: '{raw_response}'")
        
        # Test the cleaning
        cleaned_response = model_manager._clean_response(raw_response, user_input)
        print(f"   Cleaned response: '{cleaned_response}'")
        print(f"   Expected: '{expected}'")
        
        if cleaned_response == expected:
            print("   ✅ PASSED")
        else:
            print("   ❌ FAILED")
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 ALL CAREER RESPONSE TESTS PASSED!")
        print("The chatbot will now respond appropriately to greetings")
        print("instead of giving irrelevant career advice.")
    else:
        print("❌ Some tests failed. Need more work on career response filtering.")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    test_career_response_fix()
