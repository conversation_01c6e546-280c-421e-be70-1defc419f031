#!/usr/bin/env python3
"""
Comprehensive test of all artifact fixes
"""

from config import ChatbotConfig
from model_manager import LLMModelManager

def test_all_artifact_fixes():
    """Test all artifact fixes comprehensively"""
    print("🔍 Testing ALL Artifact Fixes Comprehensively")
    print("=" * 70)
    
    # Load config
    config = ChatbotConfig("chatbot_config.json")
    current_model = config.get_current_model()
    
    # Create model manager (without loading the actual model)
    model_manager = LLMModelManager(
        model_name=current_model,
        use_gpu=False
    )
    
    # Apply configuration
    gen_params = config.get_generation_params(current_model)
    model_manager.update_generation_params(**gen_params)
    model_manager.prompt_format = config.get_prompt_format(current_model)
    
    # Comprehensive test cases covering all artifact types
    test_cases = [
        # Greeting responses
        {
            "category": "Greetings",
            "input": "hello",
            "raw_response": "#1.",
            "expected": "Hello! How can I help you today?"
        },
        {
            "category": "Greetings",
            "input": "hello",
            "raw_response": "# I am a student and i want to become an engineer, what should be my career path?",
            "expected": "Hello! How can I help you today?"
        },
        {
            "category": "Greetings",
            "input": "hello",
            "raw_response": '## `data-testid="read-dictionary" />` (for "Read dictionary") should be the same as "test-reading".',
            "expected": "Hello! How can I help you today?"
        },
        
        # Programming questions
        {
            "category": "Programming",
            "input": "do you know Python?",
            "raw_response": "#docomputeregistrar#adocompiler#acompileradocompiler@authorcompilera.",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        },
        {
            "category": "Programming",
            "input": "Python programming",
            "raw_response": "#somedocument#adoc#documentation.",
            "expected": "Yes, I can help with Python programming! What would you like to know?"
        },
        
        # Documentation artifacts
        {
            "category": "Documentation",
            "input": "help",
            "raw_response": "```python\ncode block\n```",
            "expected": "I'm here to help! What can I do for you?"
        },
        {
            "category": "Documentation",
            "input": "help",
            "raw_response": "<div>HTML content</div>",
            "expected": "I'm here to help! What can I do for you?"
        },
        
        # Mixed artifacts
        {
            "category": "Mixed",
            "input": "hi",
            "raw_response": "###",
            "expected": "Hello! How can I help you today?"
        },
        {
            "category": "Mixed",
            "input": "test",
            "raw_response": "   ",
            "expected": "I'm here to help! Could you please rephrase your question?"
        }
    ]
    
    print(f"Testing {len(test_cases)} comprehensive artifact cases:")
    print("-" * 70)
    
    all_passed = True
    category_results = {}
    
    for i, test_case in enumerate(test_cases, 1):
        category = test_case["category"]
        user_input = test_case["input"]
        raw_response = test_case["raw_response"]
        expected = test_case["expected"]
        
        if category not in category_results:
            category_results[category] = {"passed": 0, "total": 0}
        
        category_results[category]["total"] += 1
        
        print(f"\n{i}. [{category}] Input: '{user_input}'")
        print(f"   Raw response: '{raw_response[:60]}{'...' if len(raw_response) > 60 else ''}'")
        
        # Test the cleaning
        cleaned_response = model_manager._clean_response(raw_response, user_input)
        print(f"   Cleaned response: '{cleaned_response}'")
        print(f"   Expected: '{expected}'")
        
        if cleaned_response == expected:
            print("   ✅ PASSED")
            category_results[category]["passed"] += 1
        else:
            print("   ❌ FAILED")
            all_passed = False
    
    # Summary by category
    print(f"\n{'='*70}")
    print("📊 RESULTS BY CATEGORY:")
    print("-" * 70)
    
    for category, results in category_results.items():
        passed = results["passed"]
        total = results["total"]
        percentage = (passed / total) * 100 if total > 0 else 0
        status = "✅" if passed == total else "❌"
        print(f"{status} {category}: {passed}/{total} ({percentage:.1f}%)")
    
    print(f"\n{'='*70}")
    if all_passed:
        print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
        print("Your chatbot will now handle ALL types of irrelevant artifacts correctly:")
        print("  ✅ Greeting responses work properly")
        print("  ✅ Programming questions get relevant answers")
        print("  ✅ Documentation artifacts are cleaned")
        print("  ✅ HTML/testing artifacts are removed")
        print("  ✅ Career advice artifacts are filtered")
        print("  ✅ Compiler artifacts are handled")
        print("  ✅ All edge cases covered")
    else:
        print("❌ Some tests failed. The artifact cleaning system needs more work.")
    
    print(f"{'='*70}")

if __name__ == "__main__":
    test_all_artifact_fixes()
