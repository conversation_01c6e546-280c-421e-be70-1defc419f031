#!/usr/bin/env python3
"""
Test the fixed response generation
"""

from chatbot import CLIChatbot

def test_fixed_responses():
    """Test the fixed response generation"""
    print("🔍 Testing Fixed Response Generation")
    print("=" * 50)
    
    # Create chatbot
    chatbot = CLIChatbot("chatbot_config.json")
    
    # Load model
    print("Loading model...")
    if not chatbot.load_model():
        print("❌ Failed to load model")
        return
    
    print("✅ Model loaded successfully")
    
    # Test different prompts
    test_prompts = [
        "hello, how are you?",
        "can you program Python?", 
        "do you know Python?",
        "what is 2+2?",
        "tell me a joke"
    ]
    
    for i, user_input in enumerate(test_prompts, 1):
        print(f"\n--- Test {i}: '{user_input}' ---")
        
        # Prepare prompt like chatbot does
        prompt = chatbot._prepare_prompt(user_input)
        print(f"Prepared prompt: {repr(prompt)}")
        
        # Generate response
        try:
            response = chatbot._generate_response(user_input)
            print(f"Response: '{response}'")
            
            # Check if it's the fallback
            if response == "Hello! How can I help you today?":
                print("⚠️  Got fallback response")
            elif response == "I'm here to help! Could you please rephrase your question?":
                print("⚠️  Got new fallback response")
            else:
                print("✅ Got unique response")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_fixed_responses()
